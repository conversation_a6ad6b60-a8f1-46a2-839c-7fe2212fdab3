/* Custom styles for the enhanced lightbox gallery */

/* Blurred background instead of solid black */
.yarl__container {
  background-color: rgba(0, 0, 0, 0.85) !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
}

/* Enhanced thumbnail styling */
.yarl__thumbnails_container {
  background: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border-radius: 12px !important;
  padding: 16px !important;
  margin: 16px !important;
}

/* Individual thumbnail styling */
.yarl__thumbnails_thumbnail {
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  cursor: pointer !important;
  overflow: hidden !important;
}

/* Thumbnail hover effect */
.yarl__thumbnails_thumbnail:hover {
  border-color: rgba(255, 255, 255, 0.8) !important;
  transform: scale(1.05) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

/* Active thumbnail styling */
.yarl__thumbnails_thumbnail--active {
  border-color: #707FF5 !important;
  box-shadow: 0 0 0 2px rgba(112, 127, 245, 0.3) !important;
  transform: scale(1.1) !important;
}

/* Thumbnail image styling */
.yarl__thumbnails_thumbnail img {
  object-fit: cover !important;
  width: 100% !important;
  height: 100% !important;
  transition: transform 0.3s ease !important;
}

/* Thumbnail image hover effect */
.yarl__thumbnails_thumbnail:hover img {
  transform: scale(1.1) !important;
}

/* Scrollable thumbnails track */
.yarl__thumbnails_track {
  scrollbar-width: thin !important;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent !important;
}

/* Webkit scrollbar styling for thumbnails */
.yarl__thumbnails_track::-webkit-scrollbar {
  height: 6px !important;
}

.yarl__thumbnails_track::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1) !important;
  border-radius: 3px !important;
}

.yarl__thumbnails_track::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3) !important;
  border-radius: 3px !important;
}

.yarl__thumbnails_track::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5) !important;
}

/* Video thumbnail overlay */
.yarl__thumbnails_thumbnail[data-video="true"]::after {
  content: "▶";
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  color: white !important;
  font-size: 16px !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
  pointer-events: none !important;
}

/* Smooth transitions for all elements */
.yarl__thumbnails_container * {
  transition: all 0.3s ease !important;
}

/* Enhanced vignette effect */
.yarl__thumbnails_vignette {
  background: linear-gradient(
    90deg,
    rgba(0, 0, 0, 0.8) 0%,
    transparent 15%,
    transparent 85%,
    rgba(0, 0, 0, 0.8) 100%
  ) !important;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .yarl__thumbnails_container {
    margin: 8px !important;
    padding: 12px !important;
  }
  
  .yarl__thumbnails_thumbnail {
    border-width: 1px !important;
  }
}

/* Loading state for thumbnails */
.yarl__thumbnails_thumbnail img[src=""] {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) !important;
  background-size: 200% 100% !important;
  animation: loading 1.5s infinite !important;
}

@keyframes loading {
  0% {
    background-position: 200% 0 !important;
  }
  100% {
    background-position: -200% 0 !important;
  }
}
