/**
 * Example data structure showing how to extend recommendations to support videos
 * This is for future reference when the API is updated to include video content
 */

// Current data structure (images only)
export interface CurrentRecommendation {
  title: string;
  duration: string;
  location: string;
  tags: string;
  media: {
    large: string;
    small: string[];
  };
  badge: string;
}

// Enhanced data structure (images + videos)
export interface EnhancedRecommendation {
  title: string;
  duration: string;
  location: string;
  tags: string;
  media: {
    large: MediaItem;
    small: MediaItem[];
  };
  badge: string;
}

export interface MediaItem {
  src: string;
  type: 'image' | 'video';
  thumbnail?: string; // Custom thumbnail for videos
  alt?: string;
  duration?: number; // For videos, duration in seconds
}

// Example data with mixed media
export const exampleEnhancedRecommendation: EnhancedRecommendation = {
  title: 'Adventure in Bali',
  duration: '7 nights / 8 days',
  location: 'Bali, Indonesia',
  tags: 'Adventure, Beach, Culture, Nature',
  badge: 'Popular',
  media: {
    large: {
      src: 'https://example.com/bali-main.jpg',
      type: 'image',
      alt: 'Beautiful Bali landscape'
    },
    small: [
      {
        src: 'https://example.com/bali-beach.jpg',
        type: 'image',
        alt: 'Bali beach view'
      },
      {
        src: 'https://example.com/bali-temple.jpg',
        type: 'image',
        alt: 'Traditional Balinese temple'
      },
      {
        src: 'https://example.com/bali-adventure.mp4',
        type: 'video',
        thumbnail: 'https://example.com/bali-adventure-thumb.jpg',
        alt: 'Adventure activities in Bali',
        duration: 45
      },
      {
        src: 'https://example.com/bali-sunset.jpg',
        type: 'image',
        alt: 'Sunset in Bali'
      },
      {
        src: 'https://example.com/bali-culture.mp4',
        type: 'video',
        thumbnail: 'https://example.com/bali-culture-thumb.jpg',
        alt: 'Balinese cultural experience',
        duration: 60
      }
    ]
  }
};

// Migration helper function to convert current data to enhanced format
export function migrateRecommendationData(
  current: CurrentRecommendation
): EnhancedRecommendation {
  return {
    ...current,
    media: {
      large: {
        src: current.media.large,
        type: 'image',
        alt: `${current.title} - Main image`
      },
      small: current.media.small.map((src, index) => ({
        src,
        type: 'image' as const,
        alt: `${current.title} - Image ${index + 1}`
      }))
    }
  };
}

// Helper function to detect video files
export function isVideoFile(url: string): boolean {
  const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi', '.mkv'];
  return videoExtensions.some(ext => 
    url.toLowerCase().includes(ext.toLowerCase())
  );
}

// Helper function to create lightbox slides from enhanced media
export function createLightboxSlides(media: EnhancedRecommendation['media']) {
  const allMedia = [media.large, ...media.small];
  
  return allMedia.map(item => {
    if (item.type === 'video') {
      return {
        type: 'video' as const,
        sources: [
          {
            src: item.src,
            type: 'video/mp4'
          }
        ],
        alt: item.alt || 'Video',
        thumbnail: item.thumbnail || item.src
      };
    }
    
    return {
      src: item.src,
      alt: item.alt || 'Image'
    };
  });
}
